import React, { useState } from 'react';
import { useAuthErrorHandler, useApiErrorHandler } from '@/hooks/useAuthErrorHandler';
import { api } from '@/lib/api';

/**
 * 认证错误处理示例组件
 * 展示如何在实际项目中使用新的认证错误处理机制
 */
const AuthErrorHandlingExample: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  
  // 使用认证错误处理Hook
  const { handleApiError, isAuthError, getAuthErrorMessage } = useAuthErrorHandler();
  const { withErrorHandling } = useApiErrorHandler();

  /**
   * 示例1: 手动处理API错误
   */
  const handleManualApiCall = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // 模拟API调用
      const data = await api.get('/api/web/user/profile');
      setResult('API调用成功: ' + JSON.stringify(data));
    } catch (error: any) {
      console.error('API调用失败:', error);
      
      // 检查是否为认证错误
      if (isAuthError(error)) {
        // 使用统一的认证错误处理
        handleApiError(error);
        setResult(`认证错误: ${getAuthErrorMessage(error)}`);
      } else {
        // 处理其他类型的错误
        setResult(`其他错误: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * 示例2: 使用错误处理包装器
   */
  const handleWrappedApiCall = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // 使用错误处理包装器，自动处理认证错误
      const data = await withErrorHandling(() => 
        api.get('/api/web/user/profile')
      );
      setResult('API调用成功: ' + JSON.stringify(data));
    } catch (error: any) {
      // 认证错误已经被自动处理，这里只需要处理业务逻辑错误
      if (!isAuthError(error)) {
        setResult(`业务错误: ${error.message}`);
      } else {
        setResult(`认证错误已自动处理: ${getAuthErrorMessage(error)}`);
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * 示例3: 模拟不同类型的认证错误
   */
  const simulateAuthError = async (errorType: string) => {
    setLoading(true);
    setResult('');
    
    try {
      // 模拟不同类型的认证错误
      let endpoint = '';
      switch (errorType) {
        case 'token_expired':
          endpoint = '/api/web/test/token-expired';
          break;
        case 'token_invalid':
          endpoint = '/api/web/test/token-invalid';
          break;
        case 'permission_denied':
          endpoint = '/api/web/test/permission-denied';
          break;
        case 'not_logged_in':
          endpoint = '/api/web/test/not-logged-in';
          break;
        default:
          endpoint = '/api/web/test/generic-error';
      }
      
      const data = await api.get(endpoint);
      setResult('意外成功: ' + JSON.stringify(data));
    } catch (error: any) {
      handleApiError(error);
      setResult(`模拟错误处理完成: ${errorType}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">认证错误处理示例</h1>
      
      <div className="space-y-6">
        {/* 示例1: 手动错误处理 */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">示例1: 手动错误处理</h2>
          <p className="text-gray-600 mb-3">
            手动检查和处理认证错误，适用于需要自定义错误处理逻辑的场景。
          </p>
          <button
            onClick={handleManualApiCall}
            disabled={loading}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? '调用中...' : '手动处理API调用'}
          </button>
        </div>

        {/* 示例2: 自动错误处理 */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">示例2: 自动错误处理</h2>
          <p className="text-gray-600 mb-3">
            使用错误处理包装器，自动处理认证错误，简化代码逻辑。
          </p>
          <button
            onClick={handleWrappedApiCall}
            disabled={loading}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading ? '调用中...' : '自动处理API调用'}
          </button>
        </div>

        {/* 示例3: 模拟不同认证错误 */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">示例3: 模拟认证错误</h2>
          <p className="text-gray-600 mb-3">
            模拟不同类型的认证错误，查看相应的处理效果。
          </p>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => simulateAuthError('token_expired')}
              disabled={loading}
              className="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600 disabled:opacity-50"
            >
              Token过期
            </button>
            <button
              onClick={() => simulateAuthError('token_invalid')}
              disabled={loading}
              className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 disabled:opacity-50"
            >
              Token无效
            </button>
            <button
              onClick={() => simulateAuthError('permission_denied')}
              disabled={loading}
              className="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600 disabled:opacity-50"
            >
              权限不足
            </button>
            <button
              onClick={() => simulateAuthError('not_logged_in')}
              disabled={loading}
              className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600 disabled:opacity-50"
            >
              未登录
            </button>
          </div>
        </div>

        {/* 结果显示 */}
        {result && (
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="text-md font-semibold mb-2">执行结果:</h3>
            <pre className="text-sm text-gray-700 whitespace-pre-wrap">{result}</pre>
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="mt-8 border-t pt-6">
        <h2 className="text-lg font-semibold mb-3">使用说明</h2>
        <div className="space-y-2 text-sm text-gray-600">
          <p><strong>1. 认证状态码:</strong> 系统定义了标准化的认证状态码，便于前端统一处理不同类型的认证错误。</p>
          <p><strong>2. 自动处理:</strong> 当检测到需要重新登录的错误时，系统会自动清除本地Token并跳转到登录页面。</p>
          <p><strong>3. 通知机制:</strong> 系统会根据错误类型显示相应的通知信息，提升用户体验。</p>
          <p><strong>4. Token刷新:</strong> 对于Token过期错误，系统会自动尝试使用RefreshToken刷新AccessToken。</p>
          <p><strong>5. 错误分类:</strong> 系统区分认证错误和业务错误，分别进行处理。</p>
        </div>
      </div>
    </div>
  );
};

export default AuthErrorHandlingExample;
